# Docker Deployment Guide for Bright Soft Website

This guide explains how to deploy the Bright Soft website using Docker Compose with <PERSON><PERSON><PERSON><PERSON> as a reverse proxy.

## 🚀 Quick Start

### Local Development
```bash
# Start the services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Deployment
```bash
# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Start production services
docker-compose -f docker-compose.prod.yml up -d
```

## 📁 File Structure
```
├── docker-compose.yml          # Local development setup
├── docker-compose.prod.yml     # Production setup
├── nginx.conf                  # Nginx configuration
├── .env.example               # Environment variables template
├── index.html                 # Website files
├── styles.css
├── script.js
└── Asset/                     # Website assets
```

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Domain configuration
DOMAIN=brightsoft.com
SUBDOMAIN=www.brightsoft.com
TRAEFIK_DOMAIN=traefik.brightsoft.com

# SSL/TLS configuration
ACME_EMAIL=<EMAIL>

# Environment
ENVIRONMENT=production

# Logging
LOG_LEVEL=INFO
```

### Local Development Domains
For local development, add these entries to your `/etc/hosts` file:
```
127.0.0.1 brightsoft.local
127.0.0.1 www.brightsoft.local
127.0.0.1 traefik.brightsoft.local
```

## 🌐 Services

### Traefik (Reverse Proxy)
- **Port 80**: HTTP (redirects to HTTPS)
- **Port 443**: HTTPS
- **Port 8080**: Traefik Dashboard
- **Features**:
  - Automatic SSL certificates via Let's Encrypt
  - HTTP to HTTPS redirection
  - Security headers
  - Compression

### Bright Soft Website (Nginx)
- **Features**:
  - Optimized static file serving
  - Gzip compression
  - Security headers
  - Caching strategies
  - Health check endpoint

## 🔒 Security Features

### SSL/TLS
- Automatic SSL certificate generation via Let's Encrypt
- HTTP to HTTPS redirection
- HSTS headers for enhanced security

### Security Headers
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Permissions-Policy` for privacy protection
- `Strict-Transport-Security` for HTTPS enforcement

### Access Control
- Hidden files protection
- Backup files protection
- Health check endpoint

## 📊 Monitoring & Logging

### Traefik Dashboard
Access the Traefik dashboard at:
- Local: `http://localhost:8080`
- Production: `https://traefik.yourdomain.com`

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f brightsoft-website
docker-compose logs -f traefik

# View Nginx access logs
docker exec brightsoft-website tail -f /var/log/nginx/access.log
```

## 🚀 Deployment Commands

### Development
```bash
# Start services
docker-compose up -d

# Rebuild and start
docker-compose up -d --build

# View status
docker-compose ps

# Stop services
docker-compose down
```

### Production
```bash
# Start production services
docker-compose -f docker-compose.prod.yml up -d

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Backup SSL certificates
tar -czf letsencrypt-backup-$(date +%Y%m%d).tar.gz letsencrypt/
```

## 🔧 Troubleshooting

### Common Issues

1. **SSL Certificate Issues**
   ```bash
   # Check certificate status
   docker-compose logs traefik | grep acme
   
   # Remove and regenerate certificates
   sudo rm -rf letsencrypt/
   docker-compose restart traefik
   ```

2. **Domain Not Resolving**
   - Ensure DNS records point to your server
   - Check firewall settings (ports 80, 443)
   - Verify domain configuration in .env file

3. **Website Not Loading**
   ```bash
   # Check Nginx status
   docker-compose logs brightsoft-website
   
   # Test Nginx configuration
   docker exec brightsoft-website nginx -t
   ```

### Health Checks
```bash
# Website health check
curl http://localhost/health

# Traefik API
curl http://localhost:8080/api/rawdata
```

## 📈 Performance Optimization

### Nginx Optimizations
- Gzip compression enabled
- Static file caching (1 year for assets)
- HTML caching (1 hour)
- Security headers

### Traefik Optimizations
- Compression middleware
- SSL session reuse
- HTTP/2 support

## 🔄 Updates & Maintenance

### Updating the Website
1. Update website files
2. Restart the container:
   ```bash
   docker-compose restart brightsoft-website
   ```

### Updating Docker Images
```bash
# Pull latest images
docker-compose pull

# Restart with new images
docker-compose up -d
```

### SSL Certificate Renewal
Certificates are automatically renewed by Let's Encrypt. Monitor logs for renewal status:
```bash
docker-compose logs traefik | grep -i renew
```

## 📞 Support

For issues related to:
- **Website Content**: Contact Jackie Tran (<EMAIL>)
- **Infrastructure**: Check logs and troubleshooting section above

---

**Bright Soft** - Professional IT Consulting Services
*Made with Grace*
